## zh-CN

通过 `classNames` 和 `styles` 属性为 Cascader 的各个部分设置自定义样式。

- `classNames` 和 `styles` 支持对象形式和函数形式
- 函数形式可以根据组件的 props 动态设置样式
- 支持的语义化节点包括：`root`、`prefix`、`suffix`
- 弹出层支持的语义化节点包括：`root`、`list`、`listItem`

## en-US

Customize styles for different parts of Cascader using `classNames` and `styles` properties.

- Both `classNames` and `styles` support object and function forms
- Function form allows dynamic styling based on component props
- Supported semantic nodes: `root`, `prefix`, `suffix`
- Popup supported semantic nodes: `root`, `list`, `listItem`
