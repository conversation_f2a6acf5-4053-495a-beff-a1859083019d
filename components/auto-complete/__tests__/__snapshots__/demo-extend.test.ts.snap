// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/auto-complete/demo/AutoComplete-and-Select.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap: 16px;"
>
  <div
    class="ant-flex css-var-test-id"
  >
    <div
      class="ant-select ant-select-sm ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
          <span
            class="ant-select-selection-item"
            style="visibility: hidden;"
            title="centered"
          >
            centered
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          >
            <div
              class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
            >
              <div
                class="ant-empty-image"
              >
                <svg
                  height="41"
                  viewBox="0 0 64 41"
                  width="64"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    No data
                  </title>
                  <g
                    fill="none"
                    fill-rule="evenodd"
                    transform="translate(0 1)"
                  >
                    <ellipse
                      cx="32"
                      cy="33"
                      fill="#f5f5f5"
                      rx="32"
                      ry="7"
                    />
                    <g
                      fill-rule="nonzero"
                      stroke="#d9d9d9"
                    >
                      <path
                        d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                      />
                      <path
                        d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                        fill="#fafafa"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div
                class="ant-empty-description"
              >
                No data
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-sm ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
  >
    <div
      class="ant-select ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
          <span
            class="ant-select-selection-item"
            style="visibility: hidden;"
            title="centered"
          >
            centered
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          >
            <div
              class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
            >
              <div
                class="ant-empty-image"
              >
                <svg
                  height="41"
                  viewBox="0 0 64 41"
                  width="64"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    No data
                  </title>
                  <g
                    fill="none"
                    fill-rule="evenodd"
                    transform="translate(0 1)"
                  >
                    <ellipse
                      cx="32"
                      cy="33"
                      fill="#f5f5f5"
                      rx="32"
                      ry="7"
                    />
                    <g
                      fill-rule="nonzero"
                      stroke="#d9d9d9"
                    >
                      <path
                        d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                      />
                      <path
                        d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                        fill="#fafafa"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div
                class="ant-empty-description"
              >
                No data
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
  >
    <div
      class="ant-select ant-select-lg ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
          <span
            class="ant-select-selection-item"
            style="visibility: hidden;"
            title="centered"
          >
            centered
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          >
            <div
              class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
            >
              <div
                class="ant-empty-image"
              >
                <svg
                  height="41"
                  viewBox="0 0 64 41"
                  width="64"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    No data
                  </title>
                  <g
                    fill="none"
                    fill-rule="evenodd"
                    transform="translate(0 1)"
                  >
                    <ellipse
                      cx="32"
                      cy="33"
                      fill="#f5f5f5"
                      rx="32"
                      ry="7"
                    />
                    <g
                      fill-rule="nonzero"
                      stroke="#d9d9d9"
                    >
                      <path
                        d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                      />
                      <path
                        d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                        fill="#fafafa"
                      />
                    </g>
                  </g>
                </svg>
              </div>
              <div
                class="ant-empty-description"
              >
                No data
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-lg ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/AutoComplete-and-Select.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/allowClear.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width: 200px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          UnClearable
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="test-id_list"
          role="listbox"
        />
      </div>
    </div>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-allow-clear ant-select-show-search"
    style="width: 200px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Customized clear icon
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="test-id_list"
          role="listbox"
        />
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/auto-complete/demo/allowClear.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/basic.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width: 200px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          input here
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="test-id_list"
          role="listbox"
        />
      </div>
    </div>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width: 200px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          control mode
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="test-id_list"
          role="listbox"
        />
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/auto-complete/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/certain-category.tsx extend context correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
  style="width: 250px;"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <span
          class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large ant-select-selection-search-input css-var-test-id ant-input-css-var"
        >
          <span
            class="ant-input-wrapper ant-input-group"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-input ant-input-lg ant-input-outlined"
              id="test-id"
              placeholder="input here"
              role="combobox"
              type="search"
              value=""
            />
            <span
              class="ant-input-group-addon"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only ant-input-search-button"
                type="button"
              >
                <span
                  class="ant-btn-icon"
                >
                  <span
                    aria-label="search"
                    class="anticon anticon-search"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="search"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
            </span>
          </span>
        </span>
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <div
    class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up certain-category-search-dropdown css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; width: 500px;"
  >
    <div>
      <div
        id="test-id_list"
        role="listbox"
        style="height: 0px; width: 0px; overflow: hidden;"
      >
        <div
          aria-selected="false"
          id="test-id_list_0"
          role="presentation"
        />
      </div>
      <div
        class="rc-virtual-list"
        style="position: relative;"
      >
        <div
          class="rc-virtual-list-holder"
          style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
        >
          <div>
            <div
              class="rc-virtual-list-holder-inner"
              style="display: flex; flex-direction: column;"
            >
              <div
                class="ant-select-item ant-select-item-group"
              >
                <div
                  class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                >
                  Libraries
                  <a
                    href="https://www.google.com/search?q=antd"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    more
                  </a>
                </div>
              </div>
              <div
                class="ant-select-item ant-select-item-option ant-select-item-option-grouped"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  <div
                    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                  >
                    AntDesign
                    <span>
                      <span
                        aria-label="user"
                        class="anticon anticon-user"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="user"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                          />
                        </svg>
                      </span>
                       10000
                    </span>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
              <div
                class="ant-select-item ant-select-item-option ant-select-item-option-grouped"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  <div
                    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                  >
                    AntDesign UI
                    <span>
                      <span
                        aria-label="user"
                        class="anticon anticon-user"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="user"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                          />
                        </svg>
                      </span>
                       10600
                    </span>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
              <div
                class="ant-select-item ant-select-item-group"
              >
                <div
                  class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                >
                  Solutions
                  <a
                    href="https://www.google.com/search?q=antd"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    more
                  </a>
                </div>
              </div>
              <div
                class="ant-select-item ant-select-item-option ant-select-item-option-grouped"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  <div
                    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                  >
                    AntDesign UI FAQ
                    <span>
                      <span
                        aria-label="user"
                        class="anticon anticon-user"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="user"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                          />
                        </svg>
                      </span>
                       60100
                    </span>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
              <div
                class="ant-select-item ant-select-item-option ant-select-item-option-grouped"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  <div
                    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                  >
                    AntDesign FAQ
                    <span>
                      <span
                        aria-label="user"
                        class="anticon anticon-user"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="user"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                          />
                        </svg>
                      </span>
                       30010
                    </span>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
              <div
                class="ant-select-item ant-select-item-group"
              >
                <div
                  class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                >
                  Articles
                  <a
                    href="https://www.google.com/search?q=antd"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    more
                  </a>
                </div>
              </div>
              <div
                class="ant-select-item ant-select-item-option ant-select-item-option-grouped"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  <div
                    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
                  >
                    AntDesign design language
                    <span>
                      <span
                        aria-label="user"
                        class="anticon anticon-user"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="user"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                          />
                        </svg>
                      </span>
                       100000
                    </span>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/certain-category.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/custom.tsx extend context correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
  style="width: 200px;"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <textarea
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-input ant-input-outlined css-var-test-id ant-input-css-var ant-select-selection-search-input custom"
          id="test-id"
          placeholder="input here"
          role="combobox"
          style="height: 50px;"
          type="search"
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <div
    class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
  >
    <div>
      <div
        class="ant-select-item-empty"
        id="test-id_list"
        role="listbox"
      />
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/custom.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/form-debug.tsx extend context correctly 1`] = `
<form
  class="ant-form ant-form-horizontal css-var-test-id ant-form-css-var"
  style="margin: 0px auto;"
>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="单独 AutoComplete"
        >
          单独 AutoComplete
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="test-id_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="test-id_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="test-id"
                      role="combobox"
                      type="search"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <div
                class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div>
                  <div
                    class="ant-select-item-empty"
                    id="test-id_list"
                    role="listbox"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="单独 TreeSelect"
        >
          单独 TreeSelect
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-tree-select ant-select-outlined ant-select-in-form-item css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="test-id_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="test-id_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="test-id"
                      readonly=""
                      role="combobox"
                      style="opacity: 0;"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <div
                class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-tree-select-dropdown css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div>
                  <div
                    class="ant-select-empty"
                    role="listbox"
                  >
                    <div
                      class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
                    >
                      <div
                        class="ant-empty-image"
                      >
                        <svg
                          height="41"
                          viewBox="0 0 64 41"
                          width="64"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <title>
                            No data
                          </title>
                          <g
                            fill="none"
                            fill-rule="evenodd"
                            transform="translate(0 1)"
                          >
                            <ellipse
                              cx="32"
                              cy="33"
                              fill="#f5f5f5"
                              rx="32"
                              ry="7"
                            />
                            <g
                              fill-rule="nonzero"
                              stroke="#d9d9d9"
                            >
                              <path
                                d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                              />
                              <path
                                d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                fill="#fafafa"
                              />
                            </g>
                          </g>
                        </svg>
                      </div>
                      <div
                        class="ant-empty-description"
                      >
                        No data
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select: none;"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="添加 Input.Group 正常"
        >
          添加 Input.Group 正常
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width: 30%;"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity: 0;"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-tree-select-dropdown css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-empty"
                      role="listbox"
                    >
                      <div
                        class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
                      >
                        <div
                          class="ant-empty-image"
                        >
                          <svg
                            height="41"
                            viewBox="0 0 64 41"
                            width="64"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <title>
                              No data
                            </title>
                            <g
                              fill="none"
                              fill-rule="evenodd"
                              transform="translate(0 1)"
                            >
                              <ellipse
                                cx="32"
                                cy="33"
                                fill="#f5f5f5"
                                rx="32"
                                ry="7"
                              />
                              <g
                                fill-rule="nonzero"
                                stroke="#d9d9d9"
                              >
                                <path
                                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                />
                                <path
                                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                  fill="#fafafa"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div
                          class="ant-empty-description"
                        >
                          No data
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select: none;"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        role="combobox"
                        type="search"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-item-empty"
                      id="test-id_list"
                      role="listbox"
                    />
                  </div>
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="包含 search 图标正常"
        >
          包含 search 图标正常
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <span
                      class="ant-input-affix-wrapper ant-input-outlined ant-select-selection-search-input css-var-test-id ant-input-css-var"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-input"
                        id="test-id"
                        role="combobox"
                        type="search"
                        value=""
                      />
                      <span
                        class="ant-input-suffix"
                      >
                        <span
                          aria-label="search"
                          class="anticon anticon-search"
                          role="img"
                        >
                          <svg
                            aria-hidden="true"
                            data-icon="search"
                            fill="currentColor"
                            focusable="false"
                            height="1em"
                            viewBox="64 64 896 896"
                            width="1em"
                          >
                            <path
                              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <div
                class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div>
                  <div
                    class="ant-select-item-empty"
                    id="test-id_list"
                    role="listbox"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="同时有 Input.Group 和图标发生移位"
        >
          同时有 Input.Group 和图标发生移位
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width: 30%;"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity: 0;"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-tree-select-dropdown css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-empty"
                      role="listbox"
                    >
                      <div
                        class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
                      >
                        <div
                          class="ant-empty-image"
                        >
                          <svg
                            height="41"
                            viewBox="0 0 64 41"
                            width="64"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <title>
                              No data
                            </title>
                            <g
                              fill="none"
                              fill-rule="evenodd"
                              transform="translate(0 1)"
                            >
                              <ellipse
                                cx="32"
                                cy="33"
                                fill="#f5f5f5"
                                rx="32"
                                ry="7"
                              />
                              <g
                                fill-rule="nonzero"
                                stroke="#d9d9d9"
                              >
                                <path
                                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                />
                                <path
                                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                  fill="#fafafa"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div
                          class="ant-empty-description"
                        >
                          No data
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select: none;"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <span
                        class="ant-input-affix-wrapper ant-input-outlined ant-select-selection-search-input css-var-test-id ant-input-css-var"
                      >
                        <input
                          aria-autocomplete="list"
                          aria-controls="test-id_list"
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-owns="test-id_list"
                          autocomplete="off"
                          class="ant-input"
                          id="test-id"
                          role="combobox"
                          type="search"
                          value=""
                        />
                        <span
                          class="ant-input-suffix"
                        >
                          <span
                            aria-label="search"
                            class="anticon anticon-search"
                            role="img"
                          >
                            <svg
                              aria-hidden="true"
                              data-icon="search"
                              fill="currentColor"
                              focusable="false"
                              height="1em"
                              viewBox="64 64 896 896"
                              width="1em"
                            >
                              <path
                                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                              />
                            </svg>
                          </span>
                        </span>
                      </span>
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-item-empty"
                      id="test-id_list"
                      role="listbox"
                    />
                  </div>
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="同时有 Input.Group 和 Search 组件发生移位"
        >
          同时有 Input.Group 和 Search 组件发生移位
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width: 30%;"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity: 0;"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-tree-select-dropdown css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-empty"
                      role="listbox"
                    >
                      <div
                        class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
                      >
                        <div
                          class="ant-empty-image"
                        >
                          <svg
                            height="41"
                            viewBox="0 0 64 41"
                            width="64"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <title>
                              No data
                            </title>
                            <g
                              fill="none"
                              fill-rule="evenodd"
                              transform="translate(0 1)"
                            >
                              <ellipse
                                cx="32"
                                cy="33"
                                fill="#f5f5f5"
                                rx="32"
                                ry="7"
                              />
                              <g
                                fill-rule="nonzero"
                                stroke="#d9d9d9"
                              >
                                <path
                                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                />
                                <path
                                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                  fill="#fafafa"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div
                          class="ant-empty-description"
                        >
                          No data
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select: none;"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <span
                        class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-select-selection-search-input css-var-test-id ant-input-css-var"
                      >
                        <span
                          class="ant-input-wrapper ant-input-group"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="test-id_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="test-id_list"
                            autocomplete="off"
                            class="ant-input ant-input-outlined"
                            id="test-id"
                            role="combobox"
                            type="search"
                            value=""
                          />
                          <span
                            class="ant-input-group-addon"
                          >
                            <button
                              class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
                              type="button"
                            >
                              <span
                                class="ant-btn-icon"
                              >
                                <span
                                  aria-label="search"
                                  class="anticon anticon-search"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="search"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </button>
                          </span>
                        </span>
                      </span>
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-item-empty"
                      id="test-id_list"
                      role="listbox"
                    />
                  </div>
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="Input Group 和 Button 结合"
        >
          Input Group 和 Button 结合
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width: 20%;"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity: 0;"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-tree-select-dropdown css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-empty"
                      role="listbox"
                    >
                      <div
                        class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
                      >
                        <div
                          class="ant-empty-image"
                        >
                          <svg
                            height="41"
                            viewBox="0 0 64 41"
                            width="64"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <title>
                              No data
                            </title>
                            <g
                              fill="none"
                              fill-rule="evenodd"
                              transform="translate(0 1)"
                            >
                              <ellipse
                                cx="32"
                                cy="33"
                                fill="#f5f5f5"
                                rx="32"
                                ry="7"
                              />
                              <g
                                fill-rule="nonzero"
                                stroke="#d9d9d9"
                              >
                                <path
                                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                />
                                <path
                                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                  fill="#fafafa"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div
                          class="ant-empty-description"
                        >
                          No data
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select: none;"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <span
                        class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-select-selection-search-input css-var-test-id ant-input-css-var"
                      >
                        <span
                          class="ant-input-wrapper ant-input-group"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="test-id_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="test-id_list"
                            autocomplete="off"
                            class="ant-input ant-input-outlined"
                            id="test-id"
                            role="combobox"
                            type="search"
                            value=""
                          />
                          <span
                            class="ant-input-group-addon"
                          >
                            <button
                              class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
                              type="button"
                            >
                              <span
                                class="ant-btn-icon"
                              >
                                <span
                                  aria-label="search"
                                  class="anticon anticon-search"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="search"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </button>
                          </span>
                        </span>
                      </span>
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <div
                  class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div>
                    <div
                      class="ant-select-item-empty"
                      id="test-id_list"
                      role="listbox"
                    />
                  </div>
                </div>
              </div>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span
                  class="ant-btn-icon"
                >
                  <span
                    aria-label="search"
                    class="anticon anticon-search"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="search"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                      />
                    </svg>
                  </span>
                </span>
                <span>
                  Search
                </span>
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/auto-complete/demo/form-debug.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Input.Group] \`Input.Group\` is deprecated. Please use \`Space.Compact\` instead.",
]
`;

exports[`renders components/auto-complete/demo/non-case-sensitive.tsx extend context correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
  style="width: 200px;"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          role="combobox"
          type="search"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        try to type \`b\`
      </span>
    </span>
  </div>
  <div
    class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
  >
    <div>
      <div
        id="test-id_list"
        role="listbox"
        style="height: 0px; width: 0px; overflow: hidden;"
      >
        <div
          aria-selected="false"
          id="test-id_list_0"
          role="option"
        >
          Burns Bay Road
        </div>
      </div>
      <div
        class="rc-virtual-list"
        style="position: relative;"
      >
        <div
          class="rc-virtual-list-holder"
          style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
        >
          <div>
            <div
              class="rc-virtual-list-holder-inner"
              style="display: flex; flex-direction: column;"
            >
              <div
                class="ant-select-item ant-select-item-option"
                title="Burns Bay Road"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  Burns Bay Road
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
              <div
                class="ant-select-item ant-select-item-option"
                title="Downing Street"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  Downing Street
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
              <div
                class="ant-select-item ant-select-item-option"
                title="Wall Street"
              >
                <div
                  class="ant-select-item-option-content"
                >
                  Wall Street
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-item-option-state"
                  style="user-select: none;"
                  unselectable="on"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/non-case-sensitive.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/options.tsx extend context correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
  style="width: 200px;"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          role="combobox"
          type="search"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        input here
      </span>
    </span>
  </div>
  <div
    class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
  >
    <div>
      <div
        class="ant-select-item-empty"
        id="test-id_list"
        role="listbox"
      />
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/options.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/render-panel.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-start ant-flex-gap-small ant-flex-vertical"
>
  <button
    aria-checked="false"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>
  <div
    style="padding-bottom: 0px; position: relative; min-width: 0;"
  >
    <div
      class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width: 120px; margin: 0px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="lucy"
            />
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            id="test-id_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Jack"
              aria-selected="false"
              id="test-id_list_0"
              role="option"
            >
              jack
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    class="ant-select-item ant-select-item-option"
                    title="Jack"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Jack
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    class="ant-select-item ant-select-item-option"
                    title="Lucy"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Lucy
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    class="ant-select-item ant-select-item-option"
                    title="Disabled"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Disabled
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    class="ant-select-item ant-select-item-option"
                    title="Bamboo"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Bamboo
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/render-panel.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/status.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-select ant-select-outlined ant-select-status-error ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-placeholder"
          />
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-select ant-select-outlined ant-select-status-warning ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width: 200px;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-placeholder"
          />
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div>
          <div
            class="ant-select-item-empty"
            id="test-id_list"
            role="listbox"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/status.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/style-class.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical css-var-test-id"
  style="column-gap: 8px; row-gap: 24px; width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
    >
      <h4>
        classNames Object
      </h4>
      <div
        class="ant-select ant-select-outlined ant-select-auto-complete demo-autocomplete-root css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
        style="width: 200px;"
      >
        <div
          class="ant-select-selector"
        >
          <span
            class="ant-select-selection-wrap"
          >
            <span
              class="ant-select-selection-search"
            >
              <input
                aria-autocomplete="list"
                aria-controls="test-id_list"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-owns="test-id_list"
                autocomplete="off"
                class="ant-select-selection-search-input demo-autocomplete-input"
                id="test-id"
                role="combobox"
                type="search"
                value=""
              />
            </span>
            <span
              class="ant-select-selection-placeholder"
            >
              input here
            </span>
          </span>
        </div>
        <div
          class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div>
            <div
              id="test-id_list"
              role="listbox"
              style="height: 0px; width: 0px; overflow: hidden;"
            >
              <div
                aria-selected="false"
                id="test-id_list_0"
                role="option"
              >
                Burns Bay Road
              </div>
            </div>
            <div
              class="rc-virtual-list"
              style="position: relative;"
            >
              <div
                class="rc-virtual-list-holder"
                style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
              >
                <div>
                  <div
                    class="rc-virtual-list-holder-inner"
                    style="display: flex; flex-direction: column;"
                  >
                    <div
                      class="ant-select-item ant-select-item-option"
                      title="Burns Bay Road"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Burns Bay Road
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                    <div
                      class="ant-select-item ant-select-item-option"
                      title="Downing Street"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Downing Street
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                    <div
                      class="ant-select-item ant-select-item-option"
                      title="Wall Street"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Wall Street
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
    >
      <h4>
        styles Object
      </h4>
      <div
        class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
        style="border-width: 2px; border-style: dashed; padding: 4px; width: 200px;"
      >
        <div
          class="ant-select-selector"
        >
          <span
            class="ant-select-selection-wrap"
          >
            <span
              class="ant-select-selection-search"
            >
              <input
                aria-autocomplete="list"
                aria-controls="test-id_list"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-owns="test-id_list"
                autocomplete="off"
                class="ant-select-selection-search-input"
                id="test-id"
                role="combobox"
                style="font-weight: bold; color: rgb(24, 144, 255);"
                type="search"
                value=""
              />
            </span>
            <span
              class="ant-select-selection-placeholder"
            >
              input here
            </span>
          </span>
        </div>
        <div
          class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div>
            <div
              id="test-id_list"
              role="listbox"
              style="height: 0px; width: 0px; overflow: hidden;"
            >
              <div
                aria-selected="false"
                id="test-id_list_0"
                role="option"
              >
                Burns Bay Road
              </div>
            </div>
            <div
              class="rc-virtual-list"
              style="position: relative;"
            >
              <div
                class="rc-virtual-list-holder"
                style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
              >
                <div>
                  <div
                    class="rc-virtual-list-holder-inner"
                    style="display: flex; flex-direction: column;"
                  >
                    <div
                      class="ant-select-item ant-select-item-option"
                      title="Burns Bay Road"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Burns Bay Road
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                    <div
                      class="ant-select-item ant-select-item-option"
                      title="Downing Street"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Downing Street
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                    <div
                      class="ant-select-item ant-select-item-option"
                      title="Wall Street"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Wall Street
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/style-class.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Space] \`direction\` is deprecated. Please use \`orientation\` instead.",
]
`;

exports[`renders components/auto-complete/demo/uncertain-category.tsx extend context correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
  style="width: 300px;"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <span
          class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large ant-input-search-with-button ant-select-selection-search-input css-var-test-id ant-input-css-var"
        >
          <span
            class="ant-input-wrapper ant-input-group"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-input ant-input-lg ant-input-outlined"
              id="test-id"
              placeholder="input here"
              role="combobox"
              type="search"
              value=""
            />
            <span
              class="ant-input-group-addon"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-input-search-button"
                type="button"
              >
                <span
                  class="ant-btn-icon"
                >
                  <span
                    aria-label="search"
                    class="anticon anticon-search"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="search"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
            </span>
          </span>
        </span>
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
  <div
    class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; width: 252px;"
  >
    <div>
      <div
        class="ant-select-item-empty"
        id="test-id_list"
        role="listbox"
      />
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/uncertain-category.tsx extend context correctly 2`] = `[]`;

exports[`renders components/auto-complete/demo/variant.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap: 12px;"
>
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width: 200px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Outlined
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="test-id_list"
          role="listbox"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-select ant-select-filled ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width: 200px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Filled
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="test-id_list"
          role="listbox"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-select ant-select-borderless ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width: 200px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Borderless
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="test-id_list"
          role="listbox"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/variant.tsx extend context correctly 2`] = `[]`;
