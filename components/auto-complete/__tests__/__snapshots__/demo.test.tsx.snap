// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/auto-complete/demo/AutoComplete-and-Select.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap:16px"
>
  <div
    class="ant-flex css-var-test-id"
  >
    <div
      class="ant-select ant-select-sm ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
          <span
            class="ant-select-selection-item"
            style="visibility:hidden"
            title="centered"
          >
            centered
          </span>
        </span>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select:none;-webkit-user-select:none"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-sm ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
  >
    <div
      class="ant-select ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
          <span
            class="ant-select-selection-item"
            style="visibility:hidden"
            title="centered"
          >
            centered
          </span>
        </span>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select:none;-webkit-user-select:none"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
  >
    <div
      class="ant-select ant-select-lg ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
          <span
            class="ant-select-selection-item"
            style="visibility:hidden"
            title="centered"
          >
            centered
          </span>
        </span>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select:none;-webkit-user-select:none"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-lg ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="centered"
            />
          </span>
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/allowClear.tsx correctly 1`] = `
Array [
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width:200px"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          UnClearable
        </span>
      </span>
    </div>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-allow-clear ant-select-show-search"
    style="width:200px"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Customized clear icon
        </span>
      </span>
    </div>
  </div>,
]
`;

exports[`renders components/auto-complete/demo/basic.tsx correctly 1`] = `
Array [
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width:200px"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          input here
        </span>
      </span>
    </div>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width:200px"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          control mode
        </span>
      </span>
    </div>
  </div>,
]
`;

exports[`renders components/auto-complete/demo/certain-category.tsx correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
  style="width:250px"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <span
          class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large ant-select-selection-search-input css-var-test-id ant-input-css-var"
        >
          <span
            class="ant-input-wrapper ant-input-group"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-input ant-input-lg ant-input-outlined"
              id="test-id"
              placeholder="input here"
              role="combobox"
              type="search"
              value=""
            />
            <span
              class="ant-input-group-addon"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-lg ant-btn-icon-only ant-input-search-button"
                type="button"
              >
                <span
                  class="ant-btn-icon"
                >
                  <span
                    aria-label="search"
                    class="anticon anticon-search"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="search"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
            </span>
          </span>
        </span>
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/custom.tsx correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
  style="width:200px"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <textarea
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-input ant-input-outlined css-var-test-id ant-input-css-var ant-select-selection-search-input custom"
          id="test-id"
          placeholder="input here"
          role="combobox"
          style="height:50px"
          type="search"
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/form-debug.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal css-var-test-id ant-form-css-var"
  style="margin:0 auto"
>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="单独 AutoComplete"
        >
          单独 AutoComplete
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="test-id_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="test-id_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="test-id"
                      role="combobox"
                      type="search"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="单独 TreeSelect"
        >
          单独 TreeSelect
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-tree-select ant-select-outlined ant-select-in-form-item css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="test-id_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="test-id_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="test-id"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="添加 Input.Group 正常"
        >
          添加 Input.Group 正常
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width:30%"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        role="combobox"
                        type="search"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="包含 search 图标正常"
        >
          包含 search 图标正常
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <span
                      class="ant-input-affix-wrapper ant-input-outlined ant-select-selection-search-input css-var-test-id ant-input-css-var"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-input"
                        id="test-id"
                        role="combobox"
                        type="search"
                        value=""
                      />
                      <span
                        class="ant-input-suffix"
                      >
                        <span
                          aria-label="search"
                          class="anticon anticon-search"
                          role="img"
                        >
                          <svg
                            aria-hidden="true"
                            data-icon="search"
                            fill="currentColor"
                            focusable="false"
                            height="1em"
                            viewBox="64 64 896 896"
                            width="1em"
                          >
                            <path
                              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="同时有 Input.Group 和图标发生移位"
        >
          同时有 Input.Group 和图标发生移位
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width:30%"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <span
                        class="ant-input-affix-wrapper ant-input-outlined ant-select-selection-search-input css-var-test-id ant-input-css-var"
                      >
                        <input
                          aria-autocomplete="list"
                          aria-controls="test-id_list"
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-owns="test-id_list"
                          autocomplete="off"
                          class="ant-input"
                          id="test-id"
                          role="combobox"
                          type="search"
                          value=""
                        />
                        <span
                          class="ant-input-suffix"
                        >
                          <span
                            aria-label="search"
                            class="anticon anticon-search"
                            role="img"
                          >
                            <svg
                              aria-hidden="true"
                              data-icon="search"
                              fill="currentColor"
                              focusable="false"
                              height="1em"
                              viewBox="64 64 896 896"
                              width="1em"
                            >
                              <path
                                d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                              />
                            </svg>
                          </span>
                        </span>
                      </span>
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="同时有 Input.Group 和 Search 组件发生移位"
        >
          同时有 Input.Group 和 Search 组件发生移位
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width:30%"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <span
                        class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-select-selection-search-input css-var-test-id ant-input-css-var"
                      >
                        <span
                          class="ant-input-wrapper ant-input-group"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="test-id_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="test-id_list"
                            autocomplete="off"
                            class="ant-input ant-input-outlined"
                            id="test-id"
                            role="combobox"
                            type="search"
                            value=""
                          />
                          <span
                            class="ant-input-group-addon"
                          >
                            <button
                              class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
                              type="button"
                            >
                              <span
                                class="ant-btn-icon"
                              >
                                <span
                                  aria-label="search"
                                  class="anticon anticon-search"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="search"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </button>
                          </span>
                        </span>
                      </span>
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8 css-var-test-id"
      >
        <label
          class=""
          title="Input Group 和 Button 结合"
        >
          Input Group 和 Button 结合
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group css-var-test-id ant-input-group-compact"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined css-var-test-id ant-select-css-var ant-tree-select-css-var ant-select-single ant-select-show-arrow"
                style="width:20%"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="test-id_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="test-id_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        id="test-id"
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <span
                        class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-select-selection-search-input css-var-test-id ant-input-css-var"
                      >
                        <span
                          class="ant-input-wrapper ant-input-group"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="test-id_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="test-id_list"
                            autocomplete="off"
                            class="ant-input ant-input-outlined"
                            id="test-id"
                            role="combobox"
                            type="search"
                            value=""
                          />
                          <span
                            class="ant-input-group-addon"
                          >
                            <button
                              class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
                              type="button"
                            >
                              <span
                                class="ant-btn-icon"
                              >
                                <span
                                  aria-label="search"
                                  class="anticon anticon-search"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="search"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </button>
                          </span>
                        </span>
                      </span>
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
              </div>
              <button
                class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="button"
              >
                <span
                  class="ant-btn-icon"
                >
                  <span
                    aria-label="search"
                    class="anticon anticon-search"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="search"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                      />
                    </svg>
                  </span>
                </span>
                <span>
                  Search
                </span>
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/auto-complete/demo/non-case-sensitive.tsx correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
  style="width:200px"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          role="combobox"
          type="search"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        try to type \`b\`
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/options.tsx correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
  style="width:200px"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <input
          aria-autocomplete="list"
          aria-controls="test-id_list"
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="test-id_list"
          autocomplete="off"
          class="ant-select-selection-search-input"
          id="test-id"
          role="combobox"
          type="search"
          value=""
        />
      </span>
      <span
        class="ant-select-selection-placeholder"
      >
        input here
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/render-panel.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-start ant-flex-gap-small ant-flex-vertical"
>
  <button
    aria-checked="false"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>
  <div
    style="padding-bottom:0;position:relative;min-width:0"
  >
    <div
      class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width:120px;margin:0"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value="lucy"
            />
          </span>
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/status.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="width:100%"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-select ant-select-outlined ant-select-status-error ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-placeholder"
          />
        </span>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-select ant-select-outlined ant-select-status-warning ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
      style="width:200px"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="test-id"
              role="combobox"
              type="search"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-placeholder"
          />
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/style-class.tsx correctly 1`] = `
<div
  class="ant-space ant-space-vertical css-var-test-id"
  style="column-gap:8px;row-gap:24px;width:100%"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
    >
      <h4>
        classNames Object
      </h4>
      <div
        class="ant-select ant-select-outlined ant-select-auto-complete demo-autocomplete-root css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
        style="width:200px"
      >
        <div
          class="ant-select-selector"
        >
          <span
            class="ant-select-selection-wrap"
          >
            <span
              class="ant-select-selection-search"
            >
              <input
                aria-autocomplete="list"
                aria-controls="test-id_list"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-owns="test-id_list"
                autocomplete="off"
                class="ant-select-selection-search-input demo-autocomplete-input"
                id="test-id"
                role="combobox"
                type="search"
                value=""
              />
            </span>
            <span
              class="ant-select-selection-placeholder"
            >
              input here
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
    >
      <h4>
        styles Object
      </h4>
      <div
        class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
        style="border-width:2px;border-style:dashed;padding:4px;width:200px"
      >
        <div
          class="ant-select-selector"
        >
          <span
            class="ant-select-selection-wrap"
          >
            <span
              class="ant-select-selection-search"
            >
              <input
                aria-autocomplete="list"
                aria-controls="test-id_list"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-owns="test-id_list"
                autocomplete="off"
                class="ant-select-selection-search-input"
                id="test-id"
                role="combobox"
                style="font-weight:bold;color:#1890ff"
                type="search"
                value=""
              />
            </span>
            <span
              class="ant-select-selection-placeholder"
            >
              input here
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/uncertain-category.tsx correctly 1`] = `
<div
  class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-customize-input ant-select-show-search"
  style="width:300px"
>
  <div
    class="ant-select-selector"
  >
    <span
      class="ant-select-selection-wrap"
    >
      <span
        class="ant-select-selection-search"
      >
        <span
          class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large ant-input-search-with-button ant-select-selection-search-input css-var-test-id ant-input-css-var"
        >
          <span
            class="ant-input-wrapper ant-input-group"
          >
            <input
              aria-autocomplete="list"
              aria-controls="test-id_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="test-id_list"
              autocomplete="off"
              class="ant-input ant-input-lg ant-input-outlined"
              id="test-id"
              placeholder="input here"
              role="combobox"
              type="search"
              value=""
            />
            <span
              class="ant-input-group-addon"
            >
              <button
                class="ant-btn css-var-test-id ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-input-search-button"
                type="button"
              >
                <span
                  class="ant-btn-icon"
                >
                  <span
                    aria-label="search"
                    class="anticon anticon-search"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="search"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
            </span>
          </span>
        </span>
      </span>
      <span
        class="ant-select-selection-placeholder"
      />
    </span>
  </div>
</div>
`;

exports[`renders components/auto-complete/demo/variant.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap:12px"
>
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width:200px"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Outlined
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-select ant-select-filled ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width:200px"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Filled
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-select ant-select-borderless ant-select-auto-complete css-var-test-id ant-select-css-var ant-select-single ant-select-show-search"
    style="width:200px"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          Borderless
        </span>
      </span>
    </div>
  </div>
</div>
`;
